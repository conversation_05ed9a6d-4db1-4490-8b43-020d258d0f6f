version: '3.8'

services:
  # Na Food Application
  na-food-app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - DATABASE_URL=mongodb+srv://admin:<EMAIL>/
      - JWT_SECRET=your-production-jwt-secret-key-here
    depends_on:
      - mongo
    networks:
      - na-food-network
    restart: "no"  # Manual start - no auto restart
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB Database (Optional - for local development)
  mongo:
    image: mongo:7.0
    container_name: na-food-mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: na_food
    volumes:
      - mongo_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - na-food-network
    restart: "no"  # Manual start - no auto restart

  # MongoDB Admin UI (Optional)
  mongo-express:
    image: mongo-express:1.0.0
    container_name: na-food-mongo-express
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: ***************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    depends_on:
      - mongo
    networks:
      - na-food-network
    restart: "no"  # Manual start - no auto restart

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: na-food-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - na-food-app
    networks:
      - na-food-network
    restart: "no"  # Manual start - no auto restart

volumes:
  mongo_data:
    driver: local

networks:
  na-food-network:
    driver: bridge