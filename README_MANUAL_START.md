# Na Food - Hướng dẫn chạy thủ công

## Tổng quan
Ứng dụng Na Food đã được cấu hình để chạy thủ công thay vì tự động. Điều này giúp bạn có toàn quyền kiểm soát khi nào khởi động và dừng ứng dụng.

## Cách chạy ứng dụng

### 1. Chạy trong môi trường Development (Phát triển)
```bash
npm run dev:manual
```
Hoặc:
```bash
node start.js
```

### 2. Chạy trong môi trường Production (Sản xuất)
Trước tiên, build ứng dụng:
```bash
npm run build
```

Sau đó chạy:
```bash
npm run start:manual
```

### 3. Chạy trực tiếp với tsx (Development)
```bash
npx tsx start.js
```

## Cách dừng ứng dụng
- Nhấn `Ctrl + C` trong terminal để dừng server
- Hoặc đóng terminal/command prompt

## Các thay đổi đã thực hiện

### 1. Xóa tự động khởi động
- ✅ Xóa các plugin Replit tự động từ `vite.config.ts`
- ✅ Chuyển đổi `server/index.ts` từ tự động chạy sang export functions
- ✅ Tạo file `start.js` để khởi động thủ công

### 2. Cập nhật package.json
- ✅ Thêm script `start:manual` và `dev:manual`
- ✅ Xóa các dependency Replit không cần thiết

### 3. Docker (Tùy chọn)
Docker vẫn có thể sử dụng nhưng cần chạy thủ công:
```bash
# Build image
docker build -t na-food .

# Chạy container
docker run -p 5000:5000 na-food
```

## Lợi ích của việc chạy thủ công
1. **Kiểm soát hoàn toàn**: Bạn quyết định khi nào khởi động/dừng
2. **Tiết kiệm tài nguyên**: Không chạy khi không cần thiết
3. **Debug dễ dàng**: Có thể xem log và lỗi rõ ràng
4. **Không phụ thuộc Replit**: Chạy được trên mọi môi trường

## Cấu trúc file mới
```
├── start.js                 # File khởi động thủ công
├── server/index.ts          # Export functions thay vì auto-run
├── package.json             # Scripts mới cho manual start
├── vite.config.ts           # Đã xóa Replit plugins
└── README_MANUAL_START.md   # Hướng dẫn này
```

## Troubleshooting
- Nếu gặp lỗi port đã được sử dụng, hãy dừng các process khác đang chạy port 5000
- Đảm bảo MongoDB đang chạy và có thể kết nối
- Kiểm tra biến môi trường DATABASE_URL và JWT_SECRET

## Liên hệ
Nếu có vấn đề gì, hãy kiểm tra log trong terminal hoặc liên hệ để được hỗ trợ.
