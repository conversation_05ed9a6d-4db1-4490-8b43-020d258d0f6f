@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(15, 100%, 60%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(142, 71%, 45%);
  --secondary-foreground: hsl(0, 0%, 100%);
  --accent: hsl(60, 4.8%, 95.9%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
  --warning: hsl(43, 96%, 56%);
  --warning-foreground: hsl(0, 0%, 100%);
  --neutral: hsl(210, 18%, 30%);
  --neutral-foreground: hsl(0, 0%, 100%);
  --light: hsl(200, 20%, 94%);
  --light-foreground: hsl(20, 14.3%, 4.1%);
  --accent-gold: hsl(51, 100%, 50%);
  --accent-gold-foreground: hsl(20, 14.3%, 4.1%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(15, 100%, 60%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(142, 71%, 45%);
  --secondary-foreground: hsl(0, 0%, 100%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
  --warning: hsl(43, 96%, 56%);
  --warning-foreground: hsl(0, 0%, 100%);
  --neutral: hsl(210, 18%, 30%);
  --neutral-foreground: hsl(0, 0%, 100%);
  --light: hsl(240, 3.7%, 15.9%);
  --light-foreground: hsl(0, 0%, 98%);
  --accent-gold: hsl(51, 100%, 50%);
  --accent-gold-foreground: hsl(240, 10%, 3.9%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  }
}

@layer components {
  .font-poppins {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  }

  .text-primary {
    color: hsl(var(--primary));
  }

  .text-secondary {
    color: hsl(var(--secondary));
  }

  .text-warning {
    color: hsl(var(--warning));
  }

  .text-neutral {
    color: hsl(var(--neutral));
  }

  .text-accent-gold {
    color: hsl(var(--accent-gold));
  }

  .bg-primary {
    background-color: hsl(var(--primary));
  }

  .bg-secondary {
    background-color: hsl(var(--secondary));
  }

  .bg-warning {
    background-color: hsl(var(--warning));
  }

  .bg-neutral {
    background-color: hsl(var(--neutral));
  }

  .bg-light {
    background-color: hsl(var(--light));
  }

  .bg-accent-gold {
    background-color: hsl(var(--accent-gold));
  }

  .hover\:bg-primary:hover {
    background-color: hsl(var(--primary));
  }

  .hover\:bg-secondary:hover {
    background-color: hsl(var(--secondary));
  }

  .hover\:text-primary:hover {
    color: hsl(var(--primary));
  }

  .hover\:text-secondary:hover {
    color: hsl(var(--secondary));
  }
}
