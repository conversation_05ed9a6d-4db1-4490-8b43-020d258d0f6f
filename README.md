# Na Food - Vietnamese Food Delivery Platform

## 🍜 Giới thiệu

Na Food là một nền tảng đặt món ăn Việt Nam hiện đại, đ<PERSON><PERSON><PERSON> xây dựng với full-stack JavaScript và hỗ trợ nhiều vai trò người dùng khác nhau.

## 🚀 Tính năng chính

- **Đa vai trò**: Admin, Staff, và Customer với quyền hạn riêng biệt
- **Quản lý sản phẩm**: <PERSON>h<PERSON><PERSON>, sử<PERSON>, xóa món ăn với hình ảnh tự động nén
- **Giỏ hàng**: Quản lý đơn hàng real-time với localStorage
- **Thanh toán**: Hỗ trợ <PERSON>, chuyển kho<PERSON>, ví điện tử
- **Đánh giá**: Hệ thống review có kiểm duyệt
- **Thống kê**: Dashboard admin với charts và báo cáo
- **Xuất báo cáo**: PDF và CSV cho đơn hàng

## 🛠️ Công nghệ sử dụng

- **Frontend**: React 18, TypeScript, Tailwind CSS, Radix UI
- **Backend**: Node.js, Express, MongoDB
- **Database**: MongoDB với auto-incrementing ID
- **Authentication**: JWT với bcrypt
- **Build Tools**: Vite, esbuild
- **Containerization**: Docker với multi-stage builds

## 📖 Hướng dẫn cài đặt

### 1. Chạy thủ công (Manual Start)
**⚠️ Ứng dụng đã được cấu hình để chạy thủ công - không tự động khởi động**

📖 **Hướng dẫn chi tiết**: [HUONG_DAN_CHAY.md](./HUONG_DAN_CHAY.md)
📋 **Manual start guide**: [README_MANUAL_START.md](./README_MANUAL_START.md)

```bash
# Cài đặt dependencies
npm install

# Chạy development server (thủ công)
npm run dev:manual

# Hoặc chạy production (sau khi build)
npm run build
npm run start:manual

# Truy cập: http://localhost:5000
```

### 2. Phát triển với VSCode
Xem hướng dẫn chi tiết: [VSCODE_GUIDE.md](./VSCODE_GUIDE.md)

### 3. Chạy với Docker (Thủ công)
Xem hướng dẫn chi tiết: [DOCKER_GUIDE.md](./DOCKER_GUIDE.md)

**⚠️ Docker đã được cấu hình để không tự động restart**

```bash
# Chạy đầy đủ với Docker Compose (thủ công)
docker-compose up

# Chỉ chạy ứng dụng (không detached)
docker-compose up na-food-app

# Để dừng: Ctrl+C hoặc
docker-compose down
```

## 🔐 Tài khoản mặc định

- **Admin**: <EMAIL> / 123456
- **Mongo Express**: admin / admin123 (khi chạy Docker)

## 📁 Cấu trúc dự án

```
na-food/
├── client/                 # React frontend
├── server/                 # Express backend
├── shared/                 # Shared schemas
├── Dockerfile             # Docker configuration
├── docker-compose.yml     # Multi-service setup
├── HUONG_DAN_CHAY.md      # 🇻🇳 Hướng dẫn chạy ứng dụng (Vietnamese)
├── README_MANUAL_START.md # Manual start guide (English)
├── VSCODE_GUIDE.md        # VSCode development guide
├── DOCKER_GUIDE.md        # Docker deployment guide
├── CLEANUP_SUMMARY.md     # Cleanup and optimization summary
└── start.js              # Manual start script
```

## 🔧 Scripts NPM

```bash
npm run dev:manual    # Development server (manual start)
npm run start:manual  # Production server (manual start)
npm run build         # Production build
npm run check         # TypeScript check
```

## 🐳 Docker Commands

```bash
# Build image
docker build -t na-food:latest .

# Run with compose (manual - no auto restart)
docker-compose up

# Run in background (if needed)
docker-compose up -d

# View logs
docker-compose logs -f na-food-app

# Stop services
docker-compose down
```

## 🌐 API Endpoints

- `GET /api/health` - Health check
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/products` - Get products
- `POST /api/orders` - Create order
- `GET /api/statistics/overview` - Admin statistics

## 📊 Monitoring

- **Health Check**: `/api/health`
- **Logs**: `docker-compose logs -f`
- **Mongo Express**: `http://localhost:8081` (Docker)
- **Nginx**: `http://localhost:80` (Production)

## 🔄 Development Workflow

1. Clone repository
2. Install dependencies: `npm install`
3. Configure environment: `.env`
4. Start development: `npm run dev:manual`
5. Build for production: `npm run build`
6. Deploy with Docker: `docker-compose up` (manual start)

## 🚀 Deployment Options

### Docker (Recommended)
- Multi-stage builds cho optimization
- Nginx reverse proxy với SSL
- MongoDB local hoặc cloud
- Manual start - no auto-restart (controlled deployment)

### Direct Node.js
- Traditional npm build và start
- Cần cài đặt MongoDB riêng
- Cần cấu hình reverse proxy

## 🔒 Security Features

- JWT authentication với 7-day expiry
- bcrypt password hashing
- Rate limiting trong Nginx
- Input validation với Zod
- CORS configuration

## 📝 Logs và Debugging

```bash
# Development logs (manual start)
npm run dev:manual

# Docker logs
docker-compose logs -f na-food-app

# Health check
curl http://localhost:5000/api/health
```

## 🤝 Contributing

1. Fork repository
2. Create feature branch
3. Commit changes
4. Create pull request

## 📞 Support

- **Issues**: GitHub Issues
- **Documentation**: `HUONG_DAN_CHAY.md` (🇻🇳), `README_MANUAL_START.md`, `CLEANUP_SUMMARY.md`
- **Guides**: `VSCODE_GUIDE.md`, `DOCKER_GUIDE.md`

## 📄 License

MIT License - Xem file LICENSE để biết thêm chi tiết.

---

**Na Food** - Bringing Vietnamese cuisine to your fingertips! 🍜🇻🇳